import base64, json, sys, google.cloud.videointelligence_v1 as vi

def detect(path:str):
    cli=vi.VideoIntelligenceServiceClient()
    content=base64.b64encode(open(path,"rb").read()).decode()
    op=cli.annotate_video(request={"features":[vi.Feature.SHOT_CHANGE_DETECTION],
                                   "input_content":content}).result()
    s=[(a.start_time_offset.total_seconds(),a.end_time_offset.total_seconds())
       for a in op.annotation_results[0].shot_annotations]
    return s

if __name__=="__main__":
    json.dump(detect(sys.argv[1]),open("shots.json","w"),indent=2)
