import os, time, json, sys, requests, dotenv, hashlib
dotenv.load_dotenv()
KEY = os.getenv("ASSEMBLYAI_API_KEY")

def debug_transcribe(path: str) -> dict:
    print(f"🔍 开始调试转录过程...")
    print(f"📁 视频文件路径: {path}")
    
    # 检查文件是否存在
    if not os.path.exists(path):
        raise FileNotFoundError(f"文件不存在: {path}")
    
    # 获取文件信息
    file_size = os.path.getsize(path)
    print(f"📊 文件大小: {file_size} 字节 ({file_size/1024/1024:.2f} MB)")
    
    # 计算文件哈希值来验证文件内容
    with open(path, "rb") as f:
        file_hash = hashlib.md5(f.read()).hexdigest()
    print(f"🔐 文件 MD5 哈希: {file_hash}")
    
    # 检查 API 密钥
    if not KEY:
        raise ValueError("ASSEMBLYAI_API_KEY 未设置")
    print(f"🔑 API 密钥已设置: {KEY[:10]}...")
    
    print("📤 上传文件到 AssemblyAI...")
    
    # 上传文件
    with open(path, "rb") as f:
        up_response = requests.post(
            "https://api.assemblyai.com/v2/upload",
            headers={"authorization": KEY},
            data=f
        )
    
    print(f"📤 上传响应状态码: {up_response.status_code}")
    
    if up_response.status_code != 200:
        print(f"❌ 上传失败: {up_response.text}")
        raise RuntimeError(f"上传失败: {up_response.status_code}")
    
    up_result = up_response.json()
    upload_url = up_result["upload_url"]
    print(f"✅ 文件上传成功")
    print(f"🔗 上传 URL: {upload_url}")
    
    # 创建转录任务
    print("🎯 创建转录任务...")
    job_response = requests.post(
        "https://api.assemblyai.com/v2/transcript",
        headers={"authorization": KEY},
        json={"audio_url": upload_url}
    )
    
    print(f"🎯 转录任务响应状态码: {job_response.status_code}")
    
    if job_response.status_code != 200:
        print(f"❌ 创建转录任务失败: {job_response.text}")
        raise RuntimeError(f"创建转录任务失败: {job_response.status_code}")
    
    job_result = job_response.json()
    job_id = job_result["id"]
    print(f"✅ 转录任务创建成功")
    print(f"🆔 任务 ID: {job_id}")
    
    # 轮询转录状态
    print("⏳ 等待转录完成...")
    while True:
        status_response = requests.get(
            f"https://api.assemblyai.com/v2/transcript/{job_id}",
            headers={"authorization": KEY}
        )
        
        if status_response.status_code != 200:
            print(f"❌ 状态查询失败: {status_response.text}")
            raise RuntimeError(f"状态查询失败: {status_response.status_code}")
        
        r = status_response.json()
        status = r["status"]
        print(f"📊 当前状态: {status}")
        
        if status == "completed":
            duration = r["audio_duration"]
            print(f"✅ 转录完成!")
            print(f"⏱️  音频时长: {duration} 秒")
            print(f"📝 转录文本预览: {r['text'][:100]}...")
            
            # 验证转录结果
            if not r['text'] or len(r['text'].strip()) == 0:
                print("⚠️  警告: 转录文本为空!")
            
            r["duration"] = duration
            return r
            
        elif status == "error":
            print(f"❌ 转录失败: {r.get('error', '未知错误')}")
            raise RuntimeError(f"转录失败: {r}")
        
        elif status in ["queued", "processing"]:
            print(f"⏳ 转录进行中... (状态: {status})")
        
        time.sleep(5)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python debug_transcribe.py <视频文件路径>")
        sys.exit(1)
    
    try:
        result = debug_transcribe(sys.argv[1])
        
        # 保存结果
        output_file = "debug_transcript.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"🎉 调试转录完成!")
        print(f"📄 结果已保存到: {output_file}")
        print(f"📝 最终文本: {result['text']}")
        
    except Exception as e:
        print(f"💥 转录过程出错: {e}")
        import traceback
        traceback.print_exc()
