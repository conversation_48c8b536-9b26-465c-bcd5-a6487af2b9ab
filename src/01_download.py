import subprocess, pathlib, sys, yt_dlp, tqdm

def download_video(link: str) -> str:
    out = pathlib.Path("demo.mp4").resolve()
    bar = tqdm.tqdm(total=100, desc="Downloading", unit="%")

    def progress_hook(d):
        if d['status'] == 'downloading':
            if 'total_bytes' in d and 'downloaded_bytes' in d:
                percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                bar.n = percent
                bar.refresh()
            elif '_percent_str' in d:
                try:
                    percent = float(d['_percent_str'].replace('%', ''))
                    bar.n = percent
                    bar.refresh()
                except (ValueError, AttributeError):
                    pass
        elif d['status'] == 'finished':
            bar.n = 100
            bar.refresh()

    # 配置 yt-dlp 选项来避免检测
    ydl_opts = {
        "outtmpl": str(out),
        "progress_hooks": [progress_hook],
        # 添加用户代理来模拟真实浏览器
        "http_headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-us,en;q=0.5",
            "Sec-Fetch-Mode": "navigate",
        },
        # 不使用 cookies，有时候会导致问题
        # "cookiesfrombrowser": ("chrome",),
        # 添加其他选项来避免检测
        "extractor_args": {
            "youtube": {
                "player_client": ["ios", "android", "web"],
                "player_skip": ["configs"],
                "skip": ["hls", "dash"]
            }
        },
        # 格式选择 - 尝试更保守的选择
        "format": "best[ext=mp4][height<=720]/best[ext=mp4]/best",
        # 重试选项
        "retries": 5,
        "fragment_retries": 5,
        # 添加更多绕过选项
        "geo_bypass": True,
        "geo_bypass_country": "US",
        # 禁用一些可能导致检测的功能
        "no_warnings": False,
        "ignoreerrors": False,
    }

    yd = yt_dlp.YoutubeDL(ydl_opts)
    yd.download([link])
    bar.close()
    return str(out)

if __name__ == "__main__":
    print(download_video(sys.argv[1]))
