import os, time, json, sys, requests, dotenv; dotenv.load_dotenv()
KEY=os.getenv("ASSEMBLYAI_API_KEY")

def transcribe(path:str)->dict:
    up=requests.post("https://api.assemblyai.com/v2/upload",
                     headers={"authorization":KEY},
                     data=open(path,"rb")).json()
    tid=requests.post("https://api.assemblyai.com/v2/transcript",
        headers={"authorization":KEY},
        json={"audio_url":up["upload_url"]}).json()["id"]
    while True:
        r=requests.get(f"https://api.assemblyai.com/v2/transcript/{tid}",
            headers={"authorization":KEY}).json()
        if r["status"]=="completed": return r
        if r["status"]=="error": raise RuntimeError(r["error"])
        time.sleep(3)

if __name__=="__main__":
    res=transcribe(sys.argv[1]); json.dump(res,open("transcript.json","w"),indent=2)
