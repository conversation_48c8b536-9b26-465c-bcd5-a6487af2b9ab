from 01_download import download_video
from 02_transcribe import transcribe
from 03_shots import detect
from 04_pick import pick
from 05_render import render

YTLINK="https://www.youtube.com/watch?v=你的AI创业视频ID"   # ←替换为正确的中文AI创业视频URL
src=download_video(YTLINK)
print("Downloaded:",src)

tr=transcribe(src)
open("transcript.json","w").write("ok")
shots=detect(src)
open("shots.json","w").write("ok")
clips=pick()
print("Picked:",clips)

# url=render(src,clips)
# print("🎉 Final vertical short:",url)
