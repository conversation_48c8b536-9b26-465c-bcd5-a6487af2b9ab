import os, time, json, sys, requests, dotenv
dotenv.load_dotenv()
KEY = os.getenv("ASSEMBLYAI_API_KEY")

def transcribe_chinese(path: str) -> dict:
    """转录中文视频"""
    print(f"🎬 转录中文视频: {path}")
    
    # 检查 API 密钥
    if not KEY:
        raise ValueError("ASSEMBLYAI_API_KEY 未设置")
    
    # 上传文件
    print("📤 上传文件...")
    with open(path, "rb") as f:
        up_response = requests.post(
            "https://api.assemblyai.com/v2/upload",
            headers={"authorization": KEY},
            data=f
        )
    
    if up_response.status_code != 200:
        raise RuntimeError(f"上传失败: {up_response.status_code} - {up_response.text}")
    
    upload_url = up_response.json()["upload_url"]
    print(f"✅ 文件上传成功")
    
    # 创建转录任务，指定中文语言
    print("🎯 创建中文转录任务...")
    job_response = requests.post(
        "https://api.assemblyai.com/v2/transcript",
        headers={"authorization": KEY},
        json={
            "audio_url": upload_url,
            "language_code": "zh",  # 指定中文
            "punctuate": True,
            "format_text": True
        }
    )
    
    if job_response.status_code != 200:
        raise RuntimeError(f"创建转录任务失败: {job_response.status_code} - {job_response.text}")
    
    job_id = job_response.json()["id"]
    print(f"✅ 转录任务创建成功，任务ID: {job_id}")
    
    # 轮询转录状态
    print("⏳ 等待转录完成...")
    while True:
        status_response = requests.get(
            f"https://api.assemblyai.com/v2/transcript/{job_id}",
            headers={"authorization": KEY}
        )
        
        if status_response.status_code != 200:
            raise RuntimeError(f"状态查询失败: {status_response.status_code}")
        
        r = status_response.json()
        status = r["status"]
        print(f"📊 当前状态: {status}")
        
        if status == "completed":
            duration = r["audio_duration"]
            print(f"✅ 转录完成!")
            print(f"⏱️  音频时长: {duration} 秒")
            print(f"🈶 语言: {r.get('language_code', 'unknown')}")
            print(f"📝 转录文本预览: {r['text'][:100]}...")
            
            r["duration"] = duration
            return r
            
        elif status == "error":
            print(f"❌ 转录失败: {r.get('error', '未知错误')}")
            raise RuntimeError(f"转录失败: {r}")
        
        time.sleep(5)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python transcribe_chinese.py <视频文件路径>")
        sys.exit(1)
    
    try:
        result = transcribe_chinese(sys.argv[1])
        
        # 保存结果
        output_file = "transcript_chinese.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"🎉 中文转录完成!")
        print(f"📄 结果已保存到: {output_file}")
        print(f"📝 完整文本:")
        print(result['text'])
        
    except Exception as e:
        print(f"💥 转录过程出错: {e}")
        import traceback
        traceback.print_exc()
