import json, math, sys

def select_clips(scored:list, total_duration:float,
                 ratio:float=0.25, ideal_len:int=25,
                 merge_gap:float=2.0):
    max_total = total_duration * ratio
    max_k     = max(2, int(max_total/ideal_len))

    cand = sorted(scored, key=lambda x: (-x["score"], x["start"]))
    chosen=[]; ttl=0
    for c in cand:
        span=c["end"]-c["start"]
        if ttl+span>max_total: continue
        chosen.append(c); ttl+=span
        if len(chosen)==max_k: break

    # sort by start & merge近邻
    chosen=sorted(chosen,key=lambda x:x["start"])
    merged=[]
    for c in chosen:
        if merged and c["start"]-merged[-1]["end"]<merge_gap:
            merged[-1]["end"]=c["end"]          # extend
        else:
            merged.append(dict(c))              # shallow copy
    return merged

if __name__=="__main__":
    scored=json.load(open("scored.json"))
    duration=json.load(open("transcript.json"))["duration"]
    clips=select_clips(scored, duration)
    json.dump(clips,open("clips.json","w"),indent=2)
