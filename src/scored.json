[{"start": 0.0, "end": 3.16, "score": 85}, {"start": 3.2, "end": 4.24, "score": 70}, {"start": 4.28, "end": 5.04, "score": 65}, {"start": 5.08, "end": 6.72, "score": 75}, {"start": 6.76, "end": 7.56, "score": 80}, {"start": 7.6, "end": 8.32, "score": 75}, {"start": 8.36, "end": 9.56, "score": 70}, {"start": 9.6, "end": 12.52, "score": 90}, {"start": 12.56, "end": 13.56, "score": 68}, {"start": 13.6, "end": 14.88, "score": 72}, {"start": 14.92, "end": 15.4, "score": 67}, {"start": 15.44, "end": 16.24, "score": 77}, {"start": 16.28, "end": 17.36, "score": 78}, {"start": 17.4, "end": 18.56, "score": 74}, {"start": 18.6, "end": 20.16, "score": 82}, {"start": 20.2, "end": 21.24, "score": 76}, {"start": 21.28, "end": 22.96, "score": 69}, {"start": 23.0, "end": 23.92, "score": 70}, {"start": 23.96, "end": 24.76, "score": 75}, {"start": 24.8, "end": 25.88, "score": 78}, {"start": 25.92, "end": 27.72, "score": 83}, {"start": 27.76, "end": 31.8, "score": 85}, {"start": 31.84, "end": 34.44, "score": 76}, {"start": 34.48, "end": 36.56, "score": 72}, {"start": 36.6, "end": 40.92, "score": 87}, {"start": 40.96, "end": 44.8, "score": 82}, {"start": 44.84, "end": 47.48, "score": 74}, {"start": 47.52, "end": 50.0, "score": 69}, {"start": 50.04, "end": 53.2, "score": 70}, {"start": 53.24, "end": 54.56, "score": 66}, {"start": 54.6, "end": 55.72, "score": 65}, {"start": 55.76, "end": 58.16, "score": 68}, {"start": 58.2, "end": 61.44, "score": 71}, {"start": 61.48, "end": 62.72, "score": 69}, {"start": 62.76, "end": 66.44, "score": 74}, {"start": 66.48, "end": 70.12, "score": 76}, {"start": 70.16, "end": 76.08, "score": 82}, {"start": 76.12, "end": 79.72, "score": 75}, {"start": 79.76, "end": 82.56, "score": 73}, {"start": 82.6, "end": 85.24, "score": 80}, {"start": 85.28, "end": 95.0, "score": 85}, {"start": 95.04, "end": 100.72, "score": 88}, {"start": 100.76, "end": 102.76, "score": 72}, {"start": 102.8, "end": 103.84, "score": 70}, {"start": 103.88, "end": 105.12, "score": 74}, {"start": 105.16, "end": 106.8, "score": 78}, {"start": 106.84, "end": 110.68, "score": 76}, {"start": 110.72, "end": 112.0, "score": 67}, {"start": 112.04, "end": 113.16, "score": 69}, {"start": 113.2, "end": 114.56, "score": 72}, {"start": 114.6, "end": 116.44, "score": 71}, {"start": 116.48, "end": 118.64, "score": 75}, {"start": 118.68, "end": 127.08, "score": 82}, {"start": 127.12, "end": 130.28, "score": 77}, {"start": 130.32, "end": 135.4, "score": 74}, {"start": 135.44, "end": 136.32, "score": 73}, {"start": 136.36, "end": 138.56, "score": 76}, {"start": 138.6, "end": 140.28, "score": 69}, {"start": 140.32, "end": 144.24, "score": 72}, {"start": 144.28, "end": 146.68, "score": 70}, {"start": 146.72, "end": 148.04, "score": 68}, {"start": 148.08, "end": 149.64, "score": 65}, {"start": 149.68, "end": 154.64, "score": 69}, {"start": 154.68, "end": 158.44, "score": 73}, {"start": 158.48, "end": 160.12, "score": 74}, {"start": 160.16, "end": 169.4, "score": 76}, {"start": 169.44, "end": 179.8, "score": 85}, {"start": 179.84, "end": 181.64, "score": 70}, {"start": 181.68, "end": 187.36, "score": 66}, {"start": 187.4, "end": 190.48, "score": 65}, {"start": 190.52, "end": 195.72, "score": 67}, {"start": 195.76, "end": 196.96, "score": 70}, {"start": 197.0, "end": 202.2, "score": 75}, {"start": 202.24, "end": 210.96, "score": 78}, {"start": 211.0, "end": 212.04, "score": 73}, {"start": 212.08, "end": 215.88, "score": 71}, {"start": 215.92, "end": 222.28, "score": 76}, {"start": 222.32, "end": 226.68, "score": 75}, {"start": 226.72, "end": 231.8, "score": 68}, {"start": 231.84, "end": 235.24, "score": 70}, {"start": 235.28, "end": 240.28, "score": 72}, {"start": 240.32, "end": 244.24, "score": 71}, {"start": 244.28, "end": 246.28, "score": 67}, {"start": 246.32, "end": 246.68, "score": 66}, {"start": 246.72, "end": 247.92, "score": 70}, {"start": 247.96, "end": 250.32, "score": 69}, {"start": 250.36, "end": 254.2, "score": 67}, {"start": 254.24, "end": 256.64, "score": 70}, {"start": 256.68, "end": 257.52, "score": 65}, {"start": 257.56, "end": 259.44, "score": 68}, {"start": 259.48, "end": 262.36, "score": 72}, {"start": 262.4, "end": 263.72, "score": 75}, {"start": 263.76, "end": 270.24, "score": 80}, {"start": 270.28, "end": 270.56, "score": 62}, {"start": 270.6, "end": 274.12, "score": 66}, {"start": 274.16, "end": 275.28, "score": 64}, {"start": 275.32, "end": 280.0, "score": 68}, {"start": 280.04, "end": 284.32, "score": 73}, {"start": 284.36, "end": 285.4, "score": 70}, {"start": 285.44, "end": 286.44, "score": 65}, {"start": 286.48, "end": 292.0, "score": 76}, {"start": 292.04, "end": 298.08, "score": 78}, {"start": 298.12, "end": 302.2, "score": 70}, {"start": 302.24, "end": 303.44, "score": 67}, {"start": 303.48, "end": 311.24, "score": 72}, {"start": 311.28, "end": 312.6, "score": 64}, {"start": 312.64, "end": 314.4, "score": 66}, {"start": 314.44, "end": 315.64, "score": 69}, {"start": 315.68, "end": 319.28, "score": 70}, {"start": 319.32, "end": 323.32, "score": 75}, {"start": 323.36, "end": 329.6, "score": 77}, {"start": 329.64, "end": 331.04, "score": 73}, {"start": 331.08, "end": 334.36, "score": 68}, {"start": 334.4, "end": 335.44, "score": 66}, {"start": 335.48, "end": 347.36, "score": 80}, {"start": 347.4, "end": 349.4, "score": 72}, {"start": 349.44, "end": 352.16, "score": 75}, {"start": 352.2, "end": 354.72, "score": 78}, {"start": 354.76, "end": 360.52, "score": 76}, {"start": 360.56, "end": 363.24, "score": 73}, {"start": 363.28, "end": 367.6, "score": 74}, {"start": 367.64, "end": 373.2, "score": 69}, {"start": 373.24, "end": 376.44, "score": 72}, {"start": 376.48, "end": 379.72, "score": 66}, {"start": 379.76, "end": 384.2, "score": 68}, {"start": 384.24, "end": 385.24, "score": 65}, {"start": 385.28, "end": 389.84, "score": 70}, {"start": 389.88, "end": 391.88, "score": 68}, {"start": 391.92, "end": 393.32, "score": 74}, {"start": 393.36, "end": 394.92, "score": 70}, {"start": 394.96, "end": 396.4, "score": 69}, {"start": 396.44, "end": 399.84, "score": 72}, {"start": 399.88, "end": 402.2, "score": 75}, {"start": 402.24, "end": 404.36, "score": 77}, {"start": 404.4, "end": 406.96, "score": 68}, {"start": 407.0, "end": 408.8, "score": 72}, {"start": 408.84, "end": 411.32, "score": 66}, {"start": 411.36, "end": 412.96, "score": 67}, {"start": 413.0, "end": 414.36, "score": 64}, {"start": 414.4, "end": 416.28, "score": 65}, {"start": 416.32, "end": 427.92, "score": 73}, {"start": 427.96, "end": 429.44, "score": 69}, {"start": 429.48, "end": 431.8, "score": 70}, {"start": 431.84, "end": 435.12, "score": 68}, {"start": 435.16, "end": 437.92, "score": 65}, {"start": 437.96, "end": 441.12, "score": 66}, {"start": 441.16, "end": 442.24, "score": 64}, {"start": 442.28, "end": 443.4, "score": 70}, {"start": 443.44, "end": 445.24, "score": 72}, {"start": 445.28, "end": 447.16, "score": 69}, {"start": 447.2, "end": 449.12, "score": 67}, {"start": 449.16, "end": 450.6, "score": 68}, {"start": 450.64, "end": 452.16, "score": 64}, {"start": 452.2, "end": 453.36, "score": 66}, {"start": 453.4, "end": 464.0, "score": 70}, {"start": 464.04, "end": 466.76, "score": 68}, {"start": 466.8, "end": 467.68, "score": 66}, {"start": 467.72, "end": 469.08, "score": 69}, {"start": 469.12, "end": 469.64, "score": 65}, {"start": 469.68, "end": 471.2, "score": 66}, {"start": 471.24, "end": 473.0, "score": 70}, {"start": 473.04, "end": 475.36, "score": 74}, {"start": 475.4, "end": 491.88, "score": 80}, {"start": 491.92, "end": 492.6, "score": 72}, {"start": 492.64, "end": 494.04, "score": 68}, {"start": 494.08, "end": 497.32, "score": 70}, {"start": 497.36, "end": 504.96, "score": 75}, {"start": 505.0, "end": 507.68, "score": 73}, {"start": 507.72, "end": 513.28, "score": 75}, {"start": 513.32, "end": 519.36, "score": 78}, {"start": 519.4, "end": 520.76, "score": 68}, {"start": 520.8, "end": 529.04, "score": 76}, {"start": 529.08, "end": 534.08, "score": 70}, {"start": 534.12, "end": 540.84, "score": 72}, {"start": 540.88, "end": 543.2, "score": 66}, {"start": 543.24, "end": 546.0, "score": 68}, {"start": 546.04, "end": 547.4, "score": 65}, {"start": 547.44, "end": 549.08, "score": 64}, {"start": 549.12, "end": 550.16, "score": 67}, {"start": 550.2, "end": 550.92, "score": 69}, {"start": 550.96, "end": 553.84, "score": 72}, {"start": 553.88, "end": 557.28, "score": 71}, {"start": 557.32, "end": 561.72, "score": 68}, {"start": 561.76, "end": 563.12, "score": 66}, {"start": 563.16, "end": 564.92, "score": 67}, {"start": 564.96, "end": 572.68, "score": 70}, {"start": 572.72, "end": 576.52, "score": 72}, {"start": 576.56, "end": 580.16, "score": 67}, {"start": 580.2, "end": 581.76, "score": 64}, {"start": 581.8, "end": 583.8, "score": 68}, {"start": 583.84, "end": 587.92, "score": 71}, {"start": 587.96, "end": 589.64, "score": 66}, {"start": 589.68, "end": 595.4, "score": 70}, {"start": 595.44, "end": 599.12, "score": 67}, {"start": 599.16, "end": 603.84, "score": 72}, {"start": 603.88, "end": 606.12, "score": 75}, {"start": 606.16, "end": 609.96, "score": 70}, {"start": 610.0, "end": 615.76, "score": 74}, {"start": 615.8, "end": 618.48, "score": 69}, {"start": 618.52, "end": 619.48, "score": 68}, {"start": 619.52, "end": 620.6, "score": 67}, {"start": 620.64, "end": 627.68, "score": 70}, {"start": 627.72, "end": 629.56, "score": 72}, {"start": 629.6, "end": 631.04, "score": 66}, {"start": 631.08, "end": 634.84, "score": 70}, {"start": 634.88, "end": 636.04, "score": 68}, {"start": 636.08, "end": 636.84, "score": 65}, {"start": 636.88, "end": 637.36, "score": 64}, {"start": 637.4, "end": 637.84, "score": 66}, {"start": 637.88, "end": 639.44, "score": 68}, {"start": 639.48, "end": 645.2, "score": 70}, {"start": 645.24, "end": 653.08, "score": 72}, {"start": 653.12, "end": 655.96, "score": 69}, {"start": 656.0, "end": 665.0, "score": 74}, {"start": 665.04, "end": 671.56, "score": 70}, {"start": 671.6, "end": 672.72, "score": 66}, {"start": 672.76, "end": 675.2, "score": 68}, {"start": 675.24, "end": 677.2, "score": 65}, {"start": 677.24, "end": 680.56, "score": 66}, {"start": 680.6, "end": 681.48, "score": 68}, {"start": 681.52, "end": 692.96, "score": 73}, {"start": 693.0, "end": 697.16, "score": 71}, {"start": 697.2, "end": 700.72, "score": 70}, {"start": 700.76, "end": 701.24, "score": 65}, {"start": 701.28, "end": 701.8, "score": 66}, {"start": 701.84, "end": 705.68, "score": 69}]