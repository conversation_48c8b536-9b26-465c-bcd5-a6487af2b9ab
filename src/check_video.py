import subprocess
import sys
import os

def check_video_info(video_path):
    """检查视频文件的详细信息"""
    print(f"🎬 检查视频文件: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 文件不存在: {video_path}")
        return
    
    try:
        # 使用 ffprobe 获取视频信息
        cmd = [
            'ffprobe', 
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ 无法获取视频信息，请确保安装了 ffmpeg")
            print("可以尝试手动播放视频文件来确认内容")
            return
        
        import json
        info = json.loads(result.stdout)
        
        # 显示基本信息
        format_info = info.get('format', {})
        duration = float(format_info.get('duration', 0))
        size = int(format_info.get('size', 0))
        
        print(f"📊 视频时长: {duration:.1f} 秒 ({duration/60:.1f} 分钟)")
        print(f"📊 文件大小: {size} 字节 ({size/1024/1024:.2f} MB)")
        
        # 显示视频流信息
        for stream in info.get('streams', []):
            if stream.get('codec_type') == 'video':
                width = stream.get('width', 'unknown')
                height = stream.get('height', 'unknown')
                fps = stream.get('r_frame_rate', 'unknown')
                print(f"🎥 视频分辨率: {width}x{height}")
                print(f"🎥 帧率: {fps}")
                break
        
        # 检查是否有音频流
        has_audio = any(s.get('codec_type') == 'audio' for s in info.get('streams', []))
        print(f"🔊 包含音频: {'是' if has_audio else '否'}")
        
        # 显示文件标题/元数据（如果有）
        tags = format_info.get('tags', {})
        if tags:
            print("📝 视频元数据:")
            for key, value in tags.items():
                print(f"   {key}: {value}")
        
    except Exception as e:
        print(f"❌ 检查视频信息时出错: {e}")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n🛠️ 解决方案建议:")
    print("1. 手动播放 demo.mp4 文件，确认视频内容是否正确")
    print("2. 如果内容不正确，需要重新获取正确的 demo 视频")
    print("3. 如果你有正确的视频 URL，可以重新下载:")
    print("   python 01_download.py <正确的YouTube链接>")
    print("4. 或者直接替换 demo.mp4 文件为正确的视频文件")

if __name__ == "__main__":
    video_path = sys.argv[1] if len(sys.argv) > 1 else "demo.mp4"
    check_video_info(video_path)
    suggest_solutions()
