import os, json, requests, dotenv; dotenv.load_dotenv()
OR=os.getenv("OPENROUTER_API_KEY")

PROMPT="""You are a short-form video editor.

Rules:
1. Work **shot-by-shot**. For each shot, output {"start":float,"end":float,"score":0-100}.
2. Do NOT overlap or merge shots.
3. The array must be sorted by start time.

Data:
transcript (first words) = {txt}
shot_list = {shots}
user_prompt = {user_prompt}
"""

def gpt_score(transcript:dict, shots:list, user_prompt:str=""):
    text = transcript["text"][:2500]
    payload={
      "model":"openai/gpt-4o-mini",
      "messages":[{"role":"user",
                   "content":PROMPT.format(txt=text,shots=shots,
                                           user_prompt=user_prompt)}]}
    r=requests.post("https://openrouter.ai/api/v1/chat/completions",
        headers={"Authorization":f"Bearer {OR}",
                 "HTTP-Referer":"https://example.com"},json=payload,timeout=90).json()
    return json.loads(r["choices"][0]["message"]["content"])   # full list

if __name__=="__main__":
    tr=json.load(open("transcript.json"))
    shots=json.load(open("shots.json"))
    scored=gpt_score(tr,shots)
    json.dump(scored,open("scored.json","w"),indent=2)