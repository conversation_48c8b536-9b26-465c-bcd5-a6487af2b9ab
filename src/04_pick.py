import os, json, requests, dotenv; dotenv.load_dotenv()
OR=os.getenv("OPENROUTER_API_KEY")

PROMPT="""You are a short-form video editor.

Rules:
1. Work **shot-by-shot**. For each shot, output {{"start":float,"end":float,"score":0-100}}.
2. Do NOT overlap or merge shots.
3. The array must be sorted by start time.

Data:
transcript (first words) = {txt}
shot_list = {shots}
user_prompt = {user_prompt}
"""

def gpt_score(transcript:dict, shots:list, user_prompt:str=""):
    text = transcript["text"][:2500]

    # 安全地构建提示内容，避免格式化冲突
    content = PROMPT.replace("{txt}", text).replace("{shots}", str(shots)).replace("{user_prompt}", user_prompt)

    payload={
      "model":"openai/gpt-4o-mini",
      "messages":[{"role":"user", "content": content}]}

    print("🤖 发送请求到 GPT...")
    response = requests.post("https://openrouter.ai/api/v1/chat/completions",
        headers={"Authorization":f"Bearer {OR}",
                 "HTTP-Referer":"https://example.com"},
        json=payload, timeout=90)

    print(f"📊 HTTP 状态码: {response.status_code}")

    if response.status_code != 200:
        print(f"❌ API 请求失败: {response.text}")
        raise RuntimeError(f"API 请求失败: {response.status_code}")

    r = response.json()

    if "choices" not in r or len(r["choices"]) == 0:
        print(f"❌ API 响应格式错误: {r}")
        raise RuntimeError(f"API 响应格式错误: {r}")

    content = r["choices"][0]["message"]["content"]
    print(f"🤖 GPT 响应: {content[:200]}...")

    try:
        return json.loads(content)
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析失败: {e}")
        print(f"原始响应: {content}")

        # 尝试提取 JSON 数组
        import re
        json_match = re.search(r'\[.*\]', content, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group())
            except json.JSONDecodeError:
                pass

        raise RuntimeError(f"无法解析 GPT 响应为 JSON: {content}")

if __name__=="__main__":
    tr=json.load(open("transcript.json"))
    shots=json.load(open("shots.json"))
    scored=gpt_score(tr,shots)
    json.dump(scored,open("scored.json","w"),indent=2)