import os, json, requests, dotenv; dotenv.load_dotenv()
OR=os.getenv("OPENROUTER_API_KEY")

PROMPT="""Return JSON list (max 2) of {{start,end}} seconds (<30s) that are most engaging.
transcript={txt}
shots={shots}
"""

def pick():
    txt=open("transcript.json").read()[:2000]
    shots=open("shots.json").read()[:500]
    payload={"model":"openai/gpt-4o-mini",
             "messages":[{"role":"user","content":PROMPT.format(txt=txt,shots=shots)}]}

    response = requests.post("https://openrouter.ai/api/v1/chat/completions",
        headers={"Authorization":f"Bearer {OR}","HTTP-Referer":"https://example.com"},
        json=payload,timeout=60)

    # 检查 HTTP 状态码
    if response.status_code != 200:
        print(f"HTTP 错误: {response.status_code}")
        print(f"响应内容: {response.text}")
        return []

    r = response.json()

    # 检查 API 响应结构
    if "choices" not in r or len(r["choices"]) == 0:
        print(f"API 响应错误: {r}")
        return []

    content = r["choices"][0]["message"]["content"]
    print(f"API 返回的内容: {content}")

    # 尝试解析 JSON
    try:
        return json.loads(content)
    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        print(f"原始内容: {content}")

        # 尝试提取 JSON 部分（如果内容包含其他文本）
        import re

        # 首先尝试提取 markdown 代码块中的 JSON
        # 修改正则表达式以更好地匹配多行 JSON
        json_block_match = re.search(r'```json\s*(\[[\s\S]*?\])\s*```', content)
        if json_block_match:
            json_str = json_block_match.group(1).strip()
            print(f"找到 JSON 代码块: {repr(json_str)}")
            try:
                result = json.loads(json_str)
                print(f"JSON 解析成功: {result}")
                return result
            except json.JSONDecodeError as e:
                print(f"JSON 代码块解析失败: {e}")
                print(f"JSON 字符串长度: {len(json_str)}")
                print(f"前50个字符: {repr(json_str[:50])}")
        else:
            print("未找到 JSON 代码块")

        # 如果没有找到代码块，尝试直接提取 JSON 数组
        json_match = re.search(r'\[.*\]', content, re.DOTALL)
        if json_match:
            print(f"找到 JSON 数组: {json_match.group()}")
            try:
                return json.loads(json_match.group())
            except json.JSONDecodeError as e:
                print(f"JSON 数组解析失败: {e}")
        else:
            print("未找到 JSON 数组")

        return []

if __name__=="__main__":
    json.dump(pick(),open("clips.json","w"),indent=2)
