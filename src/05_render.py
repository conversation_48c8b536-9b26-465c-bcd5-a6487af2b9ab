import json, os, requests, time, dotenv; dotenv.load_dotenv()
KEY=os.getenv("SHOTSTACK_API_KEY")

def render(video_url:str, clips:list)->str:
    print(f"使用视频 URL: {video_url}")
    print(f"片段数量: {len(clips)}")

    # 检查 API 密钥
    if not KEY:
        raise ValueError("SHOTSTACK_API_KEY 未设置")

    tmpl={"timeline":{"tracks":[{"clips":[]}]},
          "output":{"format":"mp4","resolution":"1080","fps":30}}

    for i, c in enumerate(clips):
        print(f"添加片段 {i+1}: {c['start']:.2f}s - {c['end']:.2f}s")
        tmpl["timeline"]["tracks"][0]["clips"].append({
            "asset":{"type":"video","src":video_url,"trim":c["start"]},
            "start":i * (c["end"]-c["start"]),
            "length":c["end"]-c["start"],
            "fit":"cover"})

    print("发送渲染请求...")
    response = requests.post("https://api.shotstack.io/v1/render",
        headers={"x-api-key":KEY},json=tmpl)

    print(f"HTTP 状态码: {response.status_code}")

    if response.status_code != 200 and response.status_code != 201:
        print(f"响应内容: {response.text}")
        raise RuntimeError(f"API 请求失败: {response.status_code} - {response.text}")

    try:
        r = response.json()
    except json.JSONDecodeError:
        print(f"无法解析响应: {response.text}")
        raise RuntimeError(f"无法解析 API 响应: {response.text}")

    if "response" not in r or "id" not in r["response"]:
        raise RuntimeError(f"API 响应格式错误: {r}")

    rid = r["response"]["id"]
    print(f"渲染任务 ID: {rid}")

    while True:
        print("检查渲染状态...")
        status_response = requests.get(f"https://api.shotstack.io/v1/render/{rid}",
            headers={"x-api-key":KEY})

        if status_response.status_code != 200:
            raise RuntimeError(f"状态检查失败: {status_response.status_code} - {status_response.text}")

        try:
            s = status_response.json()
        except json.JSONDecodeError:
            raise RuntimeError(f"无法解析状态响应: {status_response.text}")

        status = s["response"]["status"]
        print(f"当前状态: {status}")

        if status == "done":
            print(f"渲染完成！输出 URL: {s['response']['url']}")
            return s["response"]["url"]
        if status == "failed":
            raise RuntimeError(f"渲染失败: {s}")

        time.sleep(8)

if __name__=="__main__":
    clips=json.load(open("clips.json"))
    # 使用指定的视频 URL
    video_url = "https://r2-images.labubuwallpics.com/demo.mp4"
    print(render(video_url, clips))
