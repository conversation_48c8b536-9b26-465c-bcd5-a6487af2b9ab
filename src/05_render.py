import base64, json, os, requests, time, dotenv; dotenv.load_dotenv()
KEY=os.getenv("SHOTSTACK_API_KEY")

def render_one(src_url:str, c:dict)->str:
    print(f"🎬 渲染片段: {c['start']:.2f}s - {c['end']:.2f}s")

    # 检查 API 密钥
    if not KEY:
        raise ValueError("SHOTSTACK_API_KEY 未设置")

    tmpl={
      "timeline":{"tracks":[{
          "clips":[{
            "asset":{"type":"video","src":src_url},
            "start":0,
            "length":c["end"]-c["start"],
            "trim":{"start":c["start"]},
            "fit":"cover"}]}]},
      "output":{"format":"mp4","resolution":"1080x1920","fps":30}}

    print("📤 发送渲染请求...")
    response = requests.post("https://api.shotstack.io/v1/render",
        headers={"x-api-key":KEY}, json=tmpl)

    print(f"📊 HTTP 状态码: {response.status_code}")

    if response.status_code != 200 and response.status_code != 201:
        print(f"❌ API 请求失败: {response.text}")
        raise RuntimeError(f"API 请求失败: {response.status_code} - {response.text}")

    try:
        r = response.json()
    except json.JSONDecodeError:
        print(f"❌ 无法解析响应: {response.text}")
        raise RuntimeError(f"无法解析 API 响应: {response.text}")

    print(f"📋 API 响应: {r}")

    if "response" not in r:
        raise RuntimeError(f"API 响应格式错误，缺少 'response' 字段: {r}")

    if "id" not in r["response"]:
        raise RuntimeError(f"API 响应格式错误，缺少 'id' 字段: {r}")

    rid = r["response"]["id"]
    print(f"🆔 渲染任务 ID: {rid}")

    # 轮询渲染状态
    while True:
        print("⏳ 检查渲染状态...")
        status_response = requests.get(f"https://api.shotstack.io/v1/render/{rid}",
                                     headers={"x-api-key":KEY})

        if status_response.status_code != 200:
            print(f"❌ 状态查询失败: {status_response.text}")
            raise RuntimeError(f"状态查询失败: {status_response.status_code}")

        try:
            s = status_response.json()
        except json.JSONDecodeError:
            print(f"❌ 无法解析状态响应: {status_response.text}")
            raise RuntimeError(f"无法解析状态响应: {status_response.text}")

        if "response" not in s or "status" not in s["response"]:
            print(f"❌ 状态响应格式错误: {s}")
            raise RuntimeError(f"状态响应格式错误: {s}")

        status = s["response"]["status"]
        print(f"📊 当前状态: {status}")

        if status == "done":
            if "url" not in s["response"]:
                raise RuntimeError(f"渲染完成但缺少 URL: {s}")
            print(f"✅ 渲染完成! URL: {s['response']['url']}")
            return s["response"]["url"]

        elif status == "failed":
            print(f"❌ 渲染失败: {s}")
            raise RuntimeError(f"渲染失败: {s}")

        elif status in ["queued", "processing"]:
            print(f"⏳ 渲染进行中... (状态: {status})")

        time.sleep(8)

if __name__=="__main__":
    # 调用示例：python 05_render.py src_url clips.json
    import sys
    src_url=sys.argv[1]
    clips=json.load(open(sys.argv[2]))
    urls=[]
    for idx,c in enumerate(clips,1):
        print(f"▶ Render clip {idx}/{len(clips)} ...")
        urls.append(render_one(src_url,c))
    print("🎉 All URLs:", urls)
