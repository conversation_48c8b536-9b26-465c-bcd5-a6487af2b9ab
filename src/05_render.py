import base64, json, os, requests, time, dotenv; dotenv.load_dotenv()
KEY=os.getenv("SHOTSTACK_API_KEY")

def render_one(src_url:str, c:dict)->str:
    tmpl={
      "timeline":{"tracks":[{
          "clips":[{
            "asset":{"type":"video","src":src_url},
            "start":0,
            "length":c["end"]-c["start"],
            "trim":{"start":c["start"]},
            "fit":"cover"}]}]},
      "output":{"format":"mp4","resolution":"1080x1920","fps":30}}

    r=requests.post("https://api.shotstack.io/v1/render",
        headers={"x-api-key":KEY},json=tmpl).json()
    rid=r["response"]["id"]

    while True:
        s=requests.get(f"https://api.shotstack.io/v1/render/{rid}",
                       headers={"x-api-key":KEY}).json()
        status=s["response"]["status"]
        if status=="done": return s["response"]["url"]
        if status=="failed": raise RuntimeError(s)
        time.sleep(8)

if __name__=="__main__":
    # 调用示例：python 05_render.py src_url clips.json
    import sys
    src_url=sys.argv[1]
    clips=json.load(open(sys.argv[2]))
    urls=[]
    for idx,c in enumerate(clips,1):
        print(f"▶ Render clip {idx}/{len(clips)} ...")
        urls.append(render_one(src_url,c))
    print("🎉 All URLs:", urls)
